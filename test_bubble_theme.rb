#===============================================================================
# Test script for Bubble Theme functionality
# This script tests the light/dark mode toggle for speech bubbles
#===============================================================================

# Test 1: Check if PokemonSystem has the new bubbletheme attribute
puts "Test 1: Checking PokemonSystem.bubbletheme attribute..."
begin
  system = PokemonSystem.new
  puts "✓ PokemonSystem.bubbletheme initialized to: #{system.bubbletheme}"
  
  # Test setting the value
  system.bubbletheme = 1
  puts "✓ PokemonSystem.bubbletheme can be set to: #{system.bubbletheme}"
  
  system.bubbletheme = 0
  puts "✓ PokemonSystem.bubbletheme can be set to: #{system.bubbletheme}"
rescue => e
  puts "✗ Error with PokemonSystem.bubbletheme: #{e.message}"
end

puts "\n" + "="*50 + "\n"

# Test 2: Check if MessageConfig.pbGetBubbleSkin works correctly
puts "Test 2: Checking MessageConfig.pbGetBubbleSkin method..."
begin
  # Mock $PokemonSystem for testing
  $PokemonSystem = PokemonSystem.new
  
  # Test light theme (default)
  $PokemonSystem.bubbletheme = 0
  skin = MessageConfig.pbGetBubbleSkin
  puts "✓ Light theme skin: #{skin}"
  expected_light = "Graphics/Windowskins/bubbleskinlight"
  if skin == expected_light
    puts "✓ Light theme returns correct skin path"
  else
    puts "✗ Light theme returns wrong skin path. Expected: #{expected_light}, Got: #{skin}"
  end
  
  # Test dark theme
  $PokemonSystem.bubbletheme = 1
  skin = MessageConfig.pbGetBubbleSkin
  puts "✓ Dark theme skin: #{skin}"
  expected_dark = "Graphics/Windowskins/bubbleskindark"
  if skin == expected_dark
    puts "✓ Dark theme returns correct skin path"
  else
    puts "✗ Dark theme returns wrong skin path. Expected: #{expected_dark}, Got: #{skin}"
  end
  
  # Test when $PokemonSystem is nil (should default to light)
  $PokemonSystem = nil
  skin = MessageConfig.pbGetBubbleSkin
  puts "✓ Nil system skin: #{skin}"
  if skin == expected_light
    puts "✓ Nil system defaults to light theme"
  else
    puts "✗ Nil system doesn't default to light theme. Expected: #{expected_light}, Got: #{skin}"
  end
  
rescue => e
  puts "✗ Error with MessageConfig.pbGetBubbleSkin: #{e.message}"
end

puts "\n" + "="*50 + "\n"

# Test 3: Check if the graphics files exist
puts "Test 3: Checking if bubble skin graphics exist..."
begin
  light_path = "Graphics/Windowskins/bubbleskinlight.png"
  dark_path = "Graphics/Windowskins/bubbleskindark.png"
  
  if File.exist?(light_path)
    puts "✓ Light bubble skin file exists: #{light_path}"
  else
    puts "✗ Light bubble skin file missing: #{light_path}"
  end
  
  if File.exist?(dark_path)
    puts "✓ Dark bubble skin file exists: #{dark_path}"
  else
    puts "✗ Dark bubble skin file missing: #{dark_path}"
  end
rescue => e
  puts "✗ Error checking graphics files: #{e.message}"
end

puts "\n" + "="*50 + "\n"

# Test 4: Check if the options menu entry exists
puts "Test 4: Checking if bubble theme option is registered..."
begin
  bubble_option_found = false
  MenuHandlers.each_available(:options_menu) do |option, hash, name|
    if option == :bubble_theme
      bubble_option_found = true
      puts "✓ Bubble theme option found in menu handlers"
      puts "  Name: #{hash['name']}"
      puts "  Order: #{hash['order']}"
      puts "  Type: #{hash['type']}"
      puts "  Parameters: #{hash['parameters']}"
      puts "  Description: #{hash['description']}"
      break
    end
  end
  
  unless bubble_option_found
    puts "✗ Bubble theme option not found in menu handlers"
  end
rescue => e
  puts "✗ Error checking menu handlers: #{e.message}"
end

puts "\n" + "="*50 + "\n"
puts "Test completed!"
