# Bubble Theme Light/Dark Mode Implementation

## Overview
This implementation adds a light/dark mode toggle for speech bubbles in the BW Speech Bubble plugin. Players can now choose between light and dark themed speech bubbles through the options menu.

## Features Added

### 1. New Option in Options Menu
- **Option Name**: "Bubble Theme"
- **Location**: Options menu, positioned after "Speech Frame" (order 95)
- **Values**: Light / Dark
- **Description**: "Choose the theme for speech bubbles."

### 2. Graphics Files Required
The implementation expects graphics files in two locations:

**Bubble Skins** (`Graphics/Windowskins/`):
- `bubbleskinlight.png` - Light theme bubble skin
- `bubbleskindark.png` - Dark theme bubble skin

**Arrow Graphics** (`Graphics/Pictures/`):
- `Arrow_Up_Light.png` - Light theme up arrow
- `Arrow_Up_Dark.png` - Dark theme up arrow
- `Arrow_Down_Light.png` - Light theme down arrow
- `Arrow_Down_Dark.png` - Dark theme down arrow

### 3. Simplified Arrow System
- **Up Arrow**: Used when NPC is in upper half of screen (bubble appears below NPC)
- **Down Arrow**: Used when NPC is in lower half of screen (bubble appears above NPC)
- Automatic positioning based on NPC screen location
- No more complex directional logic - just up/down based on optimal bubble placement

### 4. Save Compatibility
- New saves will default to light theme (value 0)
- Existing saves will be automatically updated with the light theme default
- Save conversion ensures backward compatibility

## Technical Implementation

### Files Modified

#### 1. `Plugins\Scripts\016_UI\015_UI_Options.rb`
- Added `attr_accessor :bubbletheme` to PokemonSystem class
- Added `@bubbletheme = 0` to PokemonSystem initialize method
- Added new menu handler for bubble theme option

#### 2. `Plugins\BW Speech Bubble\BW Speech Bubble.rb`
- Added `MessageConfig.pbGetBubbleSkin` method for dynamic skin selection
- Added `MessageConfig.pbGetBubbleArrow` method for dynamic arrow selection
- Completely rewrote arrow positioning logic for simplified up/down system
- Modified speech bubble positioning based on NPC screen location
- Replaced complex directional logic with simple upper/lower half detection

#### 3. `Plugins\Scripts\002_Save data\005_Game_SaveConversions.rb`
- Added bubble theme initialization for existing save files

### Code Structure

#### MessageConfig.pbGetBubbleSkin Method
```ruby
def self.pbGetBubbleSkin
  if $PokemonSystem && $PokemonSystem.bubbletheme == 1
    return "Graphics/Windowskins/bubbleskindark"
  else
    return "Graphics/Windowskins/bubbleskinlight"
  end
end
```

#### MessageConfig.pbGetBubbleArrow Method
```ruby
def self.pbGetBubbleArrow(direction)
  theme_suffix = ($PokemonSystem && $PokemonSystem.bubbletheme == 1) ? "Dark" : "Light"
  case direction
  when :up
    return "Graphics/Pictures/Arrow_Up_#{theme_suffix}"
  when :down
    return "Graphics/Pictures/Arrow_Down_#{theme_suffix}"
  else
    return "Graphics/Pictures/Arrow_Down_#{theme_suffix}" # Default to down
  end
end
```

#### Simplified Arrow Positioning Logic
```ruby
# Determine if NPC is closer to top or bottom of screen
screen_center_y = Graphics.height / 2
is_npc_in_upper_half = speaker_y < screen_center_y

if is_npc_in_upper_half
  # NPC is in upper half - show bubble below with arrow pointing up
  arrow.bitmap = RPG::Cache.load_bitmap("", MessageConfig.pbGetBubbleArrow(:up))
  msgwindow.y = (Graphics.height - msgwindow.height) - 6
else
  # NPC is in lower half - show bubble above with arrow pointing down
  arrow.bitmap = RPG::Cache.load_bitmap("", MessageConfig.pbGetBubbleArrow(:down))
  msgwindow.y = 6
end
```

#### Option Menu Handler
```ruby
MenuHandlers.add(:options_menu, :bubble_theme, {
  "name"        => _INTL("Bubble Theme"),
  "order"       => 95,
  "type"        => EnumOption,
  "parameters"  => [_INTL("Light"), _INTL("Dark")],
  "description" => _INTL("Choose the theme for speech bubbles."),
  "get_proc"    => proc { next $PokemonSystem.bubbletheme },
  "set_proc"    => proc { |value, _scene| $PokemonSystem.bubbletheme = value }
})
```

## Usage Instructions

### For Players
1. Open the Options menu from the main menu
2. Navigate to "Bubble Theme" option
3. Use left/right arrows to toggle between "Light" and "Dark"
4. The setting is automatically saved

### For Developers
1. Ensure all required graphics are present:
   - Bubble skins in `Graphics/Windowskins/`
   - Arrow graphics in `Graphics/Pictures/`
2. The theme will automatically apply to all speech bubbles created with `pbCallBub()`
3. Arrow direction is automatically determined by NPC position
4. No additional code changes needed for existing bubble implementations

## Testing
A test script `test_bubble_theme.rb` is provided to verify:
- PokemonSystem attribute functionality
- MessageConfig.pbGetBubbleSkin method
- Graphics file existence
- Menu handler registration

## Compatibility Notes
- Compatible with existing BW Speech Bubble functionality
- Does not affect other window skins or speech frames
- Maintains backward compatibility with existing save files
- Works with both floating bubbles (type 1) and speech bubbles with arrows (type 2)

## Troubleshooting
- If bubbles don't change theme, ensure both graphics files exist
- If option doesn't appear in menu, check for syntax errors in Options.rb
- If save errors occur, verify save conversion is properly implemented
