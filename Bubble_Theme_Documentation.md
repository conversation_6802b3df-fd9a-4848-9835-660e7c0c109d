# Bubble Theme Light/Dark Mode Implementation

## Overview
This implementation adds a light/dark mode toggle for speech bubbles in the BW Speech Bubble plugin. Players can now choose between light and dark themed speech bubbles through the options menu.

## Features Added

### 1. New Option in Options Menu
- **Option Name**: "Bubble Theme"
- **Location**: Options menu, positioned after "Speech Frame" (order 95)
- **Values**: Light / Dark
- **Description**: "Choose the theme for speech bubbles."

### 2. Graphics Files Required
The implementation expects two graphics files in `Graphics/Windowskins/`:
- `bubbleskinlight.png` - Light theme bubble skin
- `bubbleskindark.png` - Dark theme bubble skin

### 3. Save Compatibility
- New saves will default to light theme (value 0)
- Existing saves will be automatically updated with the light theme default
- Save conversion ensures backward compatibility

## Technical Implementation

### Files Modified

#### 1. `Plugins\Scripts\016_UI\015_UI_Options.rb`
- Added `attr_accessor :bubbletheme` to PokemonSystem class
- Added `@bubbletheme = 0` to PokemonSystem initialize method
- Added new menu handler for bubble theme option

#### 2. `Plugins\BW Speech Bubble\BW Speech Bubble.rb`
- Added `MessageConfig.pbGetBubbleSkin` method
- Modified speech bubble skin assignment to use the new method
- Replaced hardcoded "frlgtextskin" with dynamic skin selection

#### 3. `Plugins\Scripts\002_Save data\005_Game_SaveConversions.rb`
- Added bubble theme initialization for existing save files

### Code Structure

#### MessageConfig.pbGetBubbleSkin Method
```ruby
def self.pbGetBubbleSkin
  if $PokemonSystem && $PokemonSystem.bubbletheme == 1
    return "Graphics/Windowskins/bubbleskindark"
  else
    return "Graphics/Windowskins/bubbleskinlight"
  end
end
```

#### Option Menu Handler
```ruby
MenuHandlers.add(:options_menu, :bubble_theme, {
  "name"        => _INTL("Bubble Theme"),
  "order"       => 95,
  "type"        => EnumOption,
  "parameters"  => [_INTL("Light"), _INTL("Dark")],
  "description" => _INTL("Choose the theme for speech bubbles."),
  "get_proc"    => proc { next $PokemonSystem.bubbletheme },
  "set_proc"    => proc { |value, _scene| $PokemonSystem.bubbletheme = value }
})
```

## Usage Instructions

### For Players
1. Open the Options menu from the main menu
2. Navigate to "Bubble Theme" option
3. Use left/right arrows to toggle between "Light" and "Dark"
4. The setting is automatically saved

### For Developers
1. Ensure both bubble skin graphics are present in `Graphics/Windowskins/`
2. The theme will automatically apply to all speech bubbles created with `pbCallBub()`
3. No additional code changes needed for existing bubble implementations

## Testing
A test script `test_bubble_theme.rb` is provided to verify:
- PokemonSystem attribute functionality
- MessageConfig.pbGetBubbleSkin method
- Graphics file existence
- Menu handler registration

## Compatibility Notes
- Compatible with existing BW Speech Bubble functionality
- Does not affect other window skins or speech frames
- Maintains backward compatibility with existing save files
- Works with both floating bubbles (type 1) and speech bubbles with arrows (type 2)

## Troubleshooting
- If bubbles don't change theme, ensure both graphics files exist
- If option doesn't appear in menu, check for syntax errors in Options.rb
- If save errors occur, verify save conversion is properly implemented
