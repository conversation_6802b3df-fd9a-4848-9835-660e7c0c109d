#-------------------------------------------------------------------------------
# BW Speech Bubbles for v21
# Updated by NoNonever
#-------------------------------------------------------------------------------
# To use, call pbCallBub(type, eventID)
#
# Where type is either 1 or 2:
# 1 - floating bubble
# 2 - speech bubble with arrow
#-------------------------------------------------------------------------------

#-------------------------------------------------------------------------------
# Class modifiers
#-------------------------------------------------------------------------------

class Game_Temp
  attr_accessor :speechbubble_bubble
  attr_accessor :speechbubble_vp
  attr_accessor :speechbubble_arrow
  attr_accessor :speechbubble_outofrange
  attr_accessor :speechbubble_talking
end

module MessageConfig
  BUBBLETEXTBASE  = Color.new(248,248,248)
  BUBBLETEXTSHADOW= Color.new(72,80,88)

  # Get the appropriate bubble skin based on the current theme setting
  def self.pbGetBubbleSkin
    if $PokemonSystem && $PokemonSystem.bubbletheme == 1
      return "Graphics/Windowskins/bubbleskindark"
    else
      return "Graphics/Windowskins/bubbleskinlight"
    end
  end

  # Get the appropriate arrow graphic based on theme and direction
  def self.pbGetBubbleArrow(direction)
    theme_suffix = ($PokemonSystem && $PokemonSystem.bubbletheme == 1) ? "Dark" : "Light"
    case direction
    when :up
      return "Graphics/Pictures/Arrow_Up_#{theme_suffix}"
    when :down
      return "Graphics/Pictures/Arrow_Down_#{theme_suffix}"
    else
      return "Graphics/Pictures/Arrow_Down_#{theme_suffix}" # Default to down
    end
  end
end

#-------------------------------------------------------------------------------
# Function modifiers
#-------------------------------------------------------------------------------

class Window_AdvancedTextPokemon
  def text=(value)
    if value != nil && value != "" && $game_temp.speechbubble_bubble && $game_temp.speechbubble_bubble > 0
      if $game_temp.speechbubble_bubble == 1
        $game_temp.speechbubble_bubble = 0
        resizeToFit2(value,400,100)
        # Use player if speaking, otherwise the event
        if $game_temp.speechbubble_talking == -1
          speaker_x = $game_player.screen_x
          speaker_y = $game_player.screen_y
        else
          speaker = $game_map.events[$game_temp.speechbubble_talking]
          speaker_x = speaker.screen_x
          speaker_y = speaker.screen_y
        end
        @x = speaker_x
        @y = speaker_y - (32 + @height)
            
        if @y > (Graphics.height-@height-2)
          @y = (Graphics.height-@height)
        elsif @y < 2
          @y = 2
        end
        if @x > (Graphics.width-@width-2)
          @x = speaker_x - @width
        elsif @x < 2
          @x = 2
        end
      else
        $game_temp.speechbubble_bubble = 0
      end
    end
    setText(value)
  end
end 

def pbRepositionMessageWindow(msgwindow, linecount=2)
  msgwindow.height=32*linecount+msgwindow.borderY
  msgwindow.y=(Graphics.height)-(msgwindow.height)
  if $game_temp && $game_temp.in_battle && !$scene.respond_to?("update_basic")
    msgwindow.y=0
  elsif $game_system && $game_system.respond_to?("message_position")
    case $game_system.message_position
    when 0  # up
      msgwindow.y=0
    when 1  # middle
      msgwindow.y=(Graphics.height/2)-(msgwindow.height/2)
    when 2
      if $game_temp.speechbubble_bubble==1
       msgwindow.setSkin(MessageConfig.pbGetBubbleSkin)
       msgwindow.height = 100
       msgwindow.width = 400
     elsif $game_temp.speechbubble_bubble==2
       msgwindow.setSkin(MessageConfig.pbGetBubbleSkin)
       msgwindow.height = 96
       msgwindow.width = Graphics.width * 0.85

       # Determine speaker and player positions for bubble placement
       if $game_temp.speechbubble_talking == -1
         speaker_y = $game_player.screen_y
         player_y = speaker_y
       else
         speaker = $game_map.events[$game_temp.speechbubble_talking]
         speaker_y = speaker.screen_y
         player_y = $game_player.screen_y
       end

       # Position bubble based on optimal placement considering both positions
       screen_center_y = Graphics.height / 2
       # Center the bubble horizontally
       msgwindow.x = (Graphics.width - msgwindow.width) / 2

       # Check if player and speaker are at similar Y levels (left/right interaction)
       y_difference = (player_y - speaker_y).abs

       if y_difference <= 32  # Player and speaker are roughly at same level (left/right)
         # Position bubble based on available screen space
         if speaker_y < screen_center_y
           # More space below - place bubble below
           msgwindow.y = [speaker_y + 80, Graphics.height - msgwindow.height - 20].min
           $game_temp.speechbubble_outofrange = true
         else
           # More space above - place bubble above
           msgwindow.y = [speaker_y - msgwindow.height - 80, 20].max
           $game_temp.speechbubble_outofrange = false
         end
       elsif speaker_y < screen_center_y
         # Speaker in upper half - show bubble below (normal case)
         msgwindow.y = (Graphics.height - msgwindow.height) - 272
         $game_temp.speechbubble_outofrange = false
       else
         # Speaker in lower half - show bubble above (normal case)
         msgwindow.y = 250
         $game_temp.speechbubble_outofrange = true
       end
      else
        msgwindow.height = 102
        msgwindow.y = Graphics.height - msgwindow.height - 6
      end
    end
  end
  if $game_system && $game_system.respond_to?("message_frame")
    if $game_system.message_frame != 0
      msgwindow.opacity = 0
    end
  end
  if $game_message
    case $game_message.background
      when 1  # dim
        msgwindow.opacity=0
      when 2  # transparent
        msgwindow.opacity=0
    end
  end
end
 
def pbCreateMessageWindow(viewport = nil, skin = nil)
  arrow = nil
  if $game_temp.speechbubble_bubble == 2 && ( $game_temp.speechbubble_talking == -1 || $game_map.events[$game_temp.speechbubble_talking] != nil)
    # Determine speaker x and y (player or event)
    if $game_temp.speechbubble_talking == -1
      speaker_x = $game_player.screen_x
      speaker_y = $game_player.screen_y
    else
      speaker = $game_map.events[$game_temp.speechbubble_talking]
      speaker_x = speaker.screen_x
      speaker_y = speaker.screen_y
    end

    # Get player position for interaction direction detection
    if $game_temp.speechbubble_talking == -1
      player_y = speaker_y
    else
      player_y = $game_player.screen_y
    end

    # Determine arrow direction based on bubble placement logic
    screen_center_y = Graphics.height / 2
    y_difference = (player_y - speaker_y).abs

    # Use same logic as bubble positioning to determine arrow direction
    if y_difference <= 32  # Left/right interaction
      if speaker_y < screen_center_y
        # Bubble below - arrow points up
        arrow_direction = :up
        arrow_y_offset = 32
      else
        # Bubble above - arrow points down
        arrow_direction = :down
        arrow_y_offset = -48
      end
    else  # Normal above/below interaction
      if speaker_y < screen_center_y
        # Bubble below - arrow points down
        arrow_direction = :down
        arrow_y_offset = -64
      else
        # Bubble above - arrow points up
        arrow_direction = :up
        arrow_y_offset = 2
      end
    end

    $game_temp.speechbubble_vp = Viewport.new(0, 0, Graphics.width, Graphics.height)
    $game_temp.speechbubble_vp.z = 999999
    arrow = Sprite.new($game_temp.speechbubble_vp)
    arrow.x = speaker_x - 8  # Center arrow on speaker/event
    arrow.y = speaker_y + arrow_y_offset
    arrow.z = 999999
    arrow.bitmap = RPG::Cache.load_bitmap("", MessageConfig.pbGetBubbleArrow(arrow_direction))
  end
  $game_temp.speechbubble_arrow = arrow
  msgwindow=Window_AdvancedTextPokemon.new("")
  if !viewport
    msgwindow.z=99999
  else
    msgwindow.viewport=viewport
  end
  msgwindow.visible=true
  msgwindow.letterbyletter=true
  msgwindow.back_opacity=MessageConfig::WINDOW_OPACITY
  pbBottomLeftLines(msgwindow,2)
  $game_temp.message_window_showing=true if $game_temp
  $game_message.visible=true if $game_message
  skin=MessageConfig.pbGetSpeechFrame() if !skin
  msgwindow.setSkin(skin)
  return msgwindow
end

def pbDisposeMessageWindow(msgwindow)
  $game_temp.message_window_showing=false if $game_temp
  $game_message.visible=false if $game_message
  msgwindow.dispose
  $game_temp.speechbubble_arrow.dispose if $game_temp.speechbubble_arrow
  $game_temp.speechbubble_vp.dispose if $game_temp.speechbubble_vp
end

def pbCallBub(type, value)
  if value == -1
    $game_temp.speechbubble_talking = -1
  else
    $game_temp.speechbubble_talking = get_character(value).id
  end
  $game_temp.speechbubble_bubble = type
end